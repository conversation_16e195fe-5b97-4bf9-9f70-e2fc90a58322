#pragma once

#include "stdafx.h"

/**
 * @brief 插件配置信息结构体
 * 
 * 用于存储从 plugin.json 文件解析的插件配置数据
 */
struct PluginConfig
{
    // 基础信息
    struct BaseInfo
    {
        QString appId;          // 应用ID
        QString appName;        // 应用名称
        int buildVersion;       // 构建版本号
        
        BaseInfo() : buildVersion(0) {}
    } baseInfo;
    
    // 运行环境信息
    struct RunEnvInfo
    {
        struct AppClassify
        {
            QString appType;            // 应用类型（通常为"plugin"）
            QStringList dependService;  // 依赖的服务列表
            QString launcherDll;        // 启动器DLL文件名
            QString launcherType;       // 启动器类型（通常为"native"）
            
            AppClassify() {}
        } appClassify;
        
        RunEnvInfo() {}
    } runEnvInfo;
    
    // 默认构造函数
    PluginConfig() {}
    
    /**
     * @brief 验证配置是否有效
     * @return 配置有效返回true，否则返回false
     */
    bool isValid() const
    {
        // 检查必需字段
        if (baseInfo.appId.isEmpty()) return false;
        if (baseInfo.appName.isEmpty()) return false;
        if (runEnvInfo.appClassify.appType.isEmpty()) return false;
        if (runEnvInfo.appClassify.launcherDll.isEmpty()) return false;
        if (runEnvInfo.appClassify.launcherType.isEmpty()) return false;
        
        return true;
    }
    
    /**
     * @brief 获取配置摘要信息（用于调试）
     * @return 配置摘要字符串
     */
    QString getSummary() const
    {
        QStringList summary;
        summary << QString("AppId: %1").arg(baseInfo.appId);
        summary << QString("AppName: %1").arg(baseInfo.appName);
        summary << QString("BuildVersion: %1").arg(baseInfo.buildVersion);
        summary << QString("AppType: %1").arg(runEnvInfo.appClassify.appType);
        summary << QString("LauncherDll: %1").arg(runEnvInfo.appClassify.launcherDll);
        summary << QString("LauncherType: %1").arg(runEnvInfo.appClassify.launcherType);
        summary << QString("DependServices: [%1]").arg(runEnvInfo.appClassify.dependService.join(", "));
        summary << QString("Valid: %1").arg(isValid() ? "true" : "false");
        
        return summary.join(", ");
    }
};

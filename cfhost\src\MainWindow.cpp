#include "stdafx.h"
#include "MainWindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // 设置窗口基本属性
    setWindowTitle("CFHost - 宿主程序");
    setMinimumSize(800, 600);
    resize(1024, 768);

    // 创建一个空的中央控件
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    qDebug() << "MainWindow initialized successfully";
}

MainWindow::~MainWindow()
{
    qDebug() << "MainWindow destroyed";
}

void MainWindow::activateWindow()
{
    qDebug() << "Activating main window";

    // 显示窗口（如果被最小化）
    if (isMinimized()) {
        showNormal();
    }

    // 将窗口置于前台
    raise();
    QMainWindow::activateWindow();

    // 在Windows上，确保窗口真正获得焦点
#ifdef Q_OS_WIN
    SetForegroundWindow(reinterpret_cast<HWND>(winId()));
#endif
}

#include "MainWindow.moc"

@echo off
echo Testing Plugin Loading Functionality
echo ====================================

REM 检查是否设置了QTDIR环境变量
if "%QTDIR%"=="" (
    echo ❌ Error: QTDIR environment variable is not set.
    echo Please run fix_qt_config.bat first to configure Qt.
    pause
    exit /b 1
)

echo Using Qt from: %QTDIR%

echo.
echo Building project...
msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed! Please fix compilation errors first.
    pause
    exit /b 1
)

echo ✓ Build successful!

echo.
echo Testing plugin loading scenarios...

echo.
echo Test 1: Load test plugin (should fail - no DLL)
echo Command: cfhost.exe /appId:test
echo Expected: Should fail gracefully and exit silently
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe /appId:test
echo Exit code: %ERRORLEVEL%

echo.
echo Test 2: Load idphoto plugin (should fail - no DLL)
echo Command: cfhost.exe /appId:idphoto
echo Expected: Should fail gracefully and exit silently
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe /appId:idphoto
echo Exit code: %ERRORLEVEL%

echo.
echo Test 3: Load non-existent plugin
echo Command: cfhost.exe /appId:nonexistent
echo Expected: Should fail gracefully and exit silently
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe /appId:nonexistent
echo Exit code: %ERRORLEVEL%

echo.
echo Test 4: No appId parameter
echo Command: cfhost.exe
echo Expected: Should fail gracefully and exit silently
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe
echo Exit code: %ERRORLEVEL%

echo.
echo Plugin Loading Test Summary:
echo ============================
echo.
echo ✓ Plugin configuration parsing implemented
echo ✓ Plugin DLL loading mechanism implemented
echo ✓ Error handling with silent failure implemented
echo ✓ Integration with HostApplication completed
echo.
echo Current plugin directory structure:
echo   plugins/test/plugin.json - Test plugin configuration
echo   plugins/idphoto/plugin.json - ID Photo plugin configuration
echo.
echo Note: All tests should fail gracefully since no actual plugin DLLs exist yet.
echo This is expected behavior. Plugin DLLs will be created in step 6.
echo.
echo Next step: Create actual plugin DLL for testing (Step 6)

pause

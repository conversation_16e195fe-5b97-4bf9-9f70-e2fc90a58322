# CFHost 测试插件文档

## 概述

第6步成功创建了一个最小可行性测试插件，用于验证宿主-插件框架的完整功能。

## 项目结构

```
cfhost/
├── test_plugin/                    # 测试插件项目
│   ├── test_plugin.vcxproj        # Visual Studio项目文件
│   ├── stdafx.h                   # 预编译头文件
│   ├── stdafx.cpp                 # 预编译头实现
│   ├── TestPlugin.h               # 测试插件头文件
│   ├── TestPlugin.cpp             # 测试插件实现
│   └── plugin.json                # Qt插件元数据
├── plugins/test/                   # 插件部署目录
│   ├── plugin.json                # 插件配置文件
│   └── test_plugin.dll            # 编译后的插件DLL
└── src/
    ├── BasicServiceProvider.h     # 基础服务提供者
    └── BasicServiceProvider.cpp   # 服务提供者实现
```

## 测试插件功能

### TestPlugin 类

实现了完整的 `IPlugin` 接口：

```cpp
class TestPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)
    Q_PLUGIN_METADATA(IID "com.cfpixelengine.cfhost.IPlugin" FILE "plugin.json")

public:
    // IPlugin 接口实现
    bool init(IServiceProvider* serviceProvider) override;
    QWidget* getWidget() override;
    bool canClose() override;
    QString getPluginName() const override;
    QString getPluginVersion() const override;
    QString getPluginDescription() const override;
    void cleanup() override;
};
```

### 插件UI功能

测试插件提供了一个简单但功能完整的UI：

1. **标题标签** - 显示插件名称
2. **状态标签** - 显示插件初始化状态
3. **测试按钮** - 演示插件功能和服务交互
4. **日志文本框** - 显示插件操作日志
5. **版本信息** - 显示插件版本

### 服务提供者集成

#### BasicServiceProvider 类

为插件提供基础服务：

```cpp
class BasicServiceProvider : public QObject, public IServiceProvider
{
public:
    QSharedPointer<IService> getService(const QString& serviceName) const override;
    bool hasService(const QString& serviceName) const override;
    QStringList getAvailableServices() const override;
    QString getProviderVersion() const override;
};
```

#### 服务功能

- 提供模拟的服务列表：`base`, `ui`, `config`
- 支持服务查询和版本信息
- 为后续服务管理功能奠定基础

## 插件生命周期

### 1. 加载阶段
- 宿主根据appId查找插件配置
- 解析 `plugin.json` 配置文件
- 使用 `QPluginLoader` 加载DLL
- 转换为 `IPlugin*` 接口

### 2. 初始化阶段
- 调用 `plugin->init(serviceProvider)`
- 插件接收服务提供者引用
- 创建插件UI控件
- 记录可用服务信息

### 3. 运行阶段
- 插件UI响应用户交互
- 通过服务提供者获取宿主服务
- 执行插件特定功能

### 4. 清理阶段
- 调用 `plugin->cleanup()`
- 释放UI资源
- 清理插件状态

## 配置文件

### plugins/test/plugin.json
```json
{
    "baseInfo": {
        "appId": "test",
        "appName": "测试插件",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": ["base"],
            "launcherDll": "test_plugin.dll",
            "launcherType": "native"
        }
    }
}
```

### test_plugin/plugin.json
```json
{
    "Keys": ["TestPlugin"],
    "MetaData": {
        "name": "Test Plugin",
        "version": "1.0.0",
        "description": "A minimal test plugin for CFHost framework validation"
    }
}
```

## 编译和部署

### 编译配置
- **输出目录**: `plugins/test/`
- **目标文件**: `test_plugin.dll`
- **平台**: x64
- **Qt版本**: Qt6

### 依赖项
- Qt6Core.lib
- Qt6Gui.lib  
- Qt6Widgets.lib
- 宿主接口头文件 (`src/common/`)

## 测试方法

### 运行测试脚本
```bash
test_plugin_complete.bat
```

### 手动测试
```bash
# 编译项目
msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64

# 运行宿主程序加载测试插件
bin\Debug\cfhost.exe /appId:test
```

### 验证要点

1. **插件加载成功** - 控制台显示加载日志
2. **UI正确显示** - 出现插件窗口
3. **服务交互正常** - 点击按钮显示服务信息
4. **生命周期完整** - 初始化和清理正常

## 技术特点

### Qt插件机制
- 使用 `Q_INTERFACES` 声明接口
- 使用 `Q_PLUGIN_METADATA` 导出插件
- 支持动态加载和卸载

### 接口设计
- 完全实现 `IPlugin` 接口
- 支持服务依赖注入
- 提供完整的生命周期管理

### 错误处理
- 初始化失败时优雅退出
- 资源清理完整
- 详细的调试日志

## 验证结果

测试插件成功验证了以下功能：

✅ **插件编译** - DLL成功编译并导出接口  
✅ **插件加载** - 宿主成功加载插件DLL  
✅ **接口转换** - 成功转换为IPlugin接口  
✅ **服务注入** - 服务提供者成功传递给插件  
✅ **UI创建** - 插件成功创建并返回UI控件  
✅ **功能交互** - 插件UI响应用户操作  
✅ **生命周期** - 初始化和清理流程完整  

## 下一步

第7步将把插件UI集成到宿主的主窗口中，实现完整的宿主-插件UI集成。

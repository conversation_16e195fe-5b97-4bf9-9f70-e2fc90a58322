#include "stdafx.h"
#include "ApplicationInstance.h"
#include "MainWindow.h"

ApplicationInstance::ApplicationInstance(QObject *parent)
    : QObject(parent)
    , m_localServer(nullptr)
    , m_mainWindow(nullptr)
{
    qDebug() << "ApplicationInstance created";
}

ApplicationInstance::~ApplicationInstance()
{
    if (m_localServer) {
        m_localServer->close();
        delete m_localServer;
        m_localServer = nullptr;
    }
    qDebug() << "ApplicationInstance destroyed";
}

QString ApplicationInstance::generateServerName(const QString& instanceId) const
{
    return QString("CFHost_%1").arg(instanceId);
}

bool ApplicationInstance::tryConnectToExisting(const QString& serverName)
{
    QLocalSocket socket;
    socket.connectToServer(serverName);
    
    if (socket.waitForConnected(1000)) {
        qDebug() << "Connected to existing instance, sending activation request";
        
        // 发送激活消息
        QByteArray data = "ACTIVATE";
        socket.write(data);
        socket.waitForBytesWritten(1000);
        socket.disconnectFromServer();
        
        return true;
    }
    
    return false;
}

bool ApplicationInstance::checkAndActivateExisting(const QString& instanceId)
{
    m_instanceId = instanceId;
    QString serverName = generateServerName(instanceId);
    
    qDebug() << "Checking for existing instance with server name:" << serverName;
    
    // 尝试连接到现有实例
    if (tryConnectToExisting(serverName)) {
        qDebug() << "Found existing instance, activation request sent";
        return false; // 应该退出当前进程
    }
    
    qDebug() << "No existing instance found";
    return true; // 可以继续运行
}

bool ApplicationInstance::startServer(const QString& instanceId)
{
    m_instanceId = instanceId;
    QString serverName = generateServerName(instanceId);
    
    // 清理可能存在的旧服务器
    QLocalServer::removeServer(serverName);
    
    m_localServer = new QLocalServer(this);
    connect(m_localServer, &QLocalServer::newConnection, 
            this, &ApplicationInstance::onNewConnection);
    
    if (m_localServer->listen(serverName)) {
        qDebug() << "Local server started successfully:" << serverName;
        return true;
    } else {
        qDebug() << "Failed to start local server:" << m_localServer->errorString();
        return false;
    }
}

void ApplicationInstance::setMainWindow(MainWindow* mainWindow)
{
    m_mainWindow = mainWindow;
}

void ApplicationInstance::onNewConnection()
{
    QLocalSocket* clientSocket = m_localServer->nextPendingConnection();
    if (clientSocket) {
        qDebug() << "New client connected";
        connect(clientSocket, &QLocalSocket::readyRead,
                this, &ApplicationInstance::onClientDataReady);
        connect(clientSocket, &QLocalSocket::disconnected,
                clientSocket, &QLocalSocket::deleteLater);
    }
}

void ApplicationInstance::onClientDataReady()
{
    QLocalSocket* clientSocket = qobject_cast<QLocalSocket*>(sender());
    if (!clientSocket) return;
    
    QByteArray data = clientSocket->readAll();
    QString message = QString::fromUtf8(data);
    
    qDebug() << "Received message from client:" << message;
    
    if (message == "ACTIVATE" && m_mainWindow) {
        qDebug() << "Activating main window";
        m_mainWindow->activateWindow();
    }
}
#include "stdafx.h"
#include "HostApplication.h"
#include "MainWindow.h"
#include "CommandLineArgs.h"
#include "CmdLineParser.h"

HostApplication::HostApplication(QObject *parent)
    : QObject(parent)
    , m_mainWindow(nullptr)
    , m_commandLineArgs(nullptr)
{
    qDebug() << "HostApplication created";
}

HostApplication::~HostApplication()
{
    if (m_mainWindow) {
        delete m_mainWindow;
        m_mainWindow = nullptr;
    }

    if (m_commandLineArgs) {
        delete m_commandLineArgs;
        m_commandLineArgs = nullptr;
    }

    qDebug() << "HostApplication destroyed";
}

bool HostApplication::initialize()
{
    qDebug() << "Initializing HostApplication...";

    // 解析命令行参数
    if (!parseCommandLine()) {
        qDebug() << "Failed to parse command line arguments";
        return false;
    }

    // 初始化UI
    if (!initializeUI()) {
        qDebug() << "Failed to initialize UI";
        return false;
    }

    qDebug() << "HostApplication initialized successfully";
    return true;
}

bool HostApplication::parseCommandLine()
{
    qDebug() << "Parsing command line arguments...";

    // 创建CommandLineArgs实例
    m_commandLineArgs = new CommandLineArgs();

    // 获取命令行参数
    QApplication* app = qobject_cast<QApplication*>(QApplication::instance());
    if (!app) {
        qDebug() << "Failed to get QApplication instance";
        return false;
    }

    QStringList arguments = app->arguments();
    QString cmdLine;

    // 跳过第一个参数（程序名），拼接其余参数
    for (int i = 1; i < arguments.size(); ++i) {
        if (i > 1) cmdLine += " ";
        cmdLine += arguments[i];
    }

    qDebug() << "Command line arguments:" << cmdLine;

    // 如果没有命令行参数，使用默认值
    if (cmdLine.isEmpty()) {
        qDebug() << "No command line arguments provided, using defaults";
        return false;
    }

    // 使用CmdLineParser解析参数
    CCmdLineParser parser;
    std::wstring wstr = cmdLine.toStdWString();
    if (!parser.Parse(wstr.c_str())) {
        qDebug() << "Failed to parse command line";
        return false;
    }

    // 输出解析结果用于验证
    qDebug() << "Parsed command line parameters:";
    CCmdLineParser::POSITION pos = parser.getFirst();
    CCmdLineParser_String sKey, sValue;
    while (!parser.isLast(pos)) {
        parser.getNext(pos, sKey, sValue);
        QString key = QString::fromStdWString(sKey.GetString());
        QString value = QString::fromStdWString(sValue.GetString());
        qDebug() << "  Key:" << key << "Value:" << value;
    }

    // 解析appId参数
    if (parser.HasKey(L"appId")) {
        LPCTSTR appId = parser.GetVal(L"appId");
        QString appIdStr = QString::fromStdWString(appId);
        m_commandLineArgs->setAppId(appIdStr);
        qDebug() << "AppId:" << appIdStr;
    } else {
        qDebug() << "Warning: No appId parameter provided";
    }

    // 解析multi参数
    if (parser.HasKey(L"multi")) {
        LPCTSTR multi = parser.GetVal(L"multi");
        QString multiStr = QString::fromStdWString(multi);
        bool isMultiInstance = (multiStr != "0");
        m_commandLineArgs->setMultiInstance(isMultiInstance);
        qDebug() << "Multi mode:" << (isMultiInstance ? "enabled" : "disabled");
    } else {
        qDebug() << "Multi mode: default (enabled)";
    }

    // 解析instanceId参数
    if (parser.HasKey(L"instanceId")) {
        LPCTSTR instanceId = parser.GetVal(L"instanceId");
        QString instanceIdStr = QString::fromStdWString(instanceId);
        m_commandLineArgs->setInstanceId(instanceIdStr);
        qDebug() << "Instance ID:" << instanceIdStr;
    } else {
        qDebug() << "Instance ID: default";
    }

    return true;
}

bool HostApplication::initializeUI()
{
    qDebug() << "Initializing UI...";
    
    // 创建主窗口
    m_mainWindow = new MainWindow();
    if (!m_mainWindow) {
        qDebug() << "Failed to create MainWindow";
        return false;
    }
    
    // 显示主窗口
    m_mainWindow->show();
    
    qDebug() << "UI initialized successfully";
    return true;
}

int HostApplication::run()
{
    qDebug() << "HostApplication running, entering event loop";
    
    // 获取QApplication实例并进入事件循环
    QApplication* app = qobject_cast<QApplication*>(QApplication::instance());
    if (!app) {
        qDebug() << "Failed to get QApplication instance";
        return 1;
    }
    
    return app->exec();
}

#include "HostApplication.moc"

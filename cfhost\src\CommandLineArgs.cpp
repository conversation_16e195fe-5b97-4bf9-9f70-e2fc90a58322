#include "stdafx.h"
#include "CommandLineArgs.h"

CommandLineArgs::CommandLineArgs()
    : m_isMultiInstance(true)  // 默认为多实例模式
    , m_instanceId("default")  // 默认实例ID
{
    qDebug() << "CommandLineArgs created with default values";
}

CommandLineArgs::~CommandLineArgs()
{
    qDebug() << "CommandLineArgs destroyed";
}

bool CommandLineArgs::isValid() const
{
    // appId是必需参数
    if (m_appId.isEmpty()) {
        return false;
    }
    
    // instanceId不能为空
    if (m_instanceId.isEmpty()) {
        return false;
    }
    
    return true;
}

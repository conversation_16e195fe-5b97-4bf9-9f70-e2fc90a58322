@echo off
echo Testing command line parameter parsing...

if not exist "bin\Debug\cfhost.exe" (
    echo Error: cfhost.exe not found. Please build the project first.
    pause
    exit /b 1
)

echo.
echo Test 1: Basic parameters
echo Command: cfhost.exe /appId:idphoto /multi:0 /instanceId:app_instance_1
bin\Debug\cfhost.exe /appId:idphoto /multi:0 /instanceId:app_instance_1

echo.
echo Test 2: Only appId parameter
echo Command: cfhost.exe /appId:test
bin\Debug\cfhost.exe /appId:test

echo.
echo Test 3: No parameters
echo Command: cfhost.exe
bin\Debug\cfhost.exe

pause

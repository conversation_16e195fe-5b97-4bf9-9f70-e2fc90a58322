@echo off
echo Testing command line parameter parsing (Updated for HostApplication)...

if not exist "bin\Debug\cfhost.exe" (
    echo Error: cfhost.exe not found. Please build the project first.
    pause
    exit /b 1
)

echo.
echo Test 1: Basic parameters
echo Command: cfhost.exe /appId:idphoto /multi:0 /instanceId:app_instance_1
echo Check console output for parsed parameters...
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe /appId:idphoto /multi:0 /instanceId:app_instance_1

echo.
echo Test 2: Only appId parameter
echo Command: cfhost.exe /appId:test
echo Check console output for parsed parameters...
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe /appId:test

echo.
echo Test 3: No parameters (should use defaults)
echo Command: cfhost.exe
echo Check console output for default parameters...
timeout /t 2 /nobreak >nul
bin\Debug\cfhost.exe

pause

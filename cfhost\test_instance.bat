@echo off
echo Testing single/multi instance functionality...

if not exist "bin\Debug\cfhost.exe" (
    echo Error: cfhost.exe not found. Please build the project first.
    pause
    exit /b 1
)

echo.
echo Test 1: Multi-instance mode (default)
echo Starting first instance...
start "CFHost Instance 1" bin\Debug\cfhost.exe /appId:test
timeout /t 2 /nobreak >nul
echo Starting second instance...
start "CFHost Instance 2" bin\Debug\cfhost.exe /appId:test
echo Both instances should be running independently.
echo.

echo Test 2: Single-instance mode
echo Starting first single instance...
start "CFHost Single 1" bin\Debug\cfhost.exe /appId:test /multi:0 /instanceId:single_test
timeout /t 2 /nobreak >nul
echo Starting second single instance (should activate first)...
start "CFHost Single 2" bin\Debug\cfhost.exe /appId:test /multi:0 /instanceId:single_test
echo The second instance should have activated the first window and exited.
echo.

echo Test 3: Different instance IDs (should create separate instances)
echo Starting first instance with instanceId:app1...
start "CFHost App1" bin\Debug\cfhost.exe /appId:test /multi:0 /instanceId:app1
timeout /t 2 /nobreak >nul
echo Starting second instance with instanceId:app2...
start "CFHost App2" bin\Debug\cfhost.exe /appId:test /multi:0 /instanceId:app2
echo Both instances should be running (different instance IDs).

echo.
echo All tests started. Check the running windows to verify behavior.
pause

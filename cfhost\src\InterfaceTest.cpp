#include "stdafx.h"
#include "common/IService.h"
#include "common/IServiceProvider.h"
#include "common/IPlugin.h"

/**
 * @brief 接口测试文件
 * 
 * 此文件用于验证所有接口头文件可以被正常包含和编译。
 * 这是一个编译时测试，不会在运行时执行。
 */

// 测试IService接口的可编译性
class TestService : public IService
{
public:
    QString getServiceName() const override { return "TestService"; }
    QString getServiceVersion() const override { return "1.0"; }
    bool initialize() override { return true; }
    void cleanup() override {}
    bool isInitialized() const override { return true; }
};

// 测试IServiceProvider接口的可编译性
class TestServiceProvider : public IServiceProvider
{
public:
    QSharedPointer<IService> getService(const QString& serviceName) const override 
    { 
        Q_UNUSED(serviceName);
        return QSharedPointer<IService>(); 
    }
    
    bool hasService(const QString& serviceName) const override 
    { 
        Q_UNUSED(serviceName);
        return false; 
    }
    
    QStringList getAvailableServices() const override 
    { 
        return QStringList(); 
    }
    
    QString getProviderVersion() const override 
    { 
        return "1.0"; 
    }
};

// 测试IPlugin接口的可编译性
class TestPlugin : public IPlugin
{
public:
    bool init(IServiceProvider* serviceProvider) override 
    { 
        Q_UNUSED(serviceProvider);
        return true; 
    }
    
    QWidget* getWidget() override 
    { 
        return nullptr; 
    }
    
    bool canClose() override 
    { 
        return true; 
    }
    
    QString getPluginName() const override 
    { 
        return "TestPlugin"; 
    }
    
    QString getPluginVersion() const override 
    { 
        return "1.0"; 
    }
    
    QString getPluginDescription() const override 
    { 
        return "Test plugin for interface validation"; 
    }
    
    void cleanup() override 
    {
    }
};

// 接口编译测试函数
void testInterfaceCompilation()
{
    // 这个函数永远不会被调用，只是用来测试接口是否可以正常编译
    TestService service;
    TestServiceProvider provider;
    TestPlugin plugin;
    
    // 测试接口方法调用
    service.getServiceName();
    provider.getProviderVersion();
    plugin.getPluginName();
    
    qDebug() << "Interface compilation test passed";
}

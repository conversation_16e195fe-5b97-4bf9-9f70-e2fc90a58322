# CFHost 插件加载与管理功能

## 功能概述

第5步实现了完整的插件加载与管理功能，包括：

1. **PluginConfig 结构体** - 存储插件配置信息
2. **PluginManager 类** - 负责插件的查找、加载和管理
3. **集成到 HostApplication** - 在应用启动时自动加载插件
4. **错误处理** - 按照PRD要求实现静默失败机制

## 核心组件

### PluginConfig 结构体

存储从 `plugin.json` 解析的插件配置数据：

```cpp
struct PluginConfig {
    struct BaseInfo {
        QString appId;          // 应用ID
        QString appName;        // 应用名称  
        int buildVersion;       // 构建版本号
    } baseInfo;
    
    struct RunEnvInfo {
        struct AppClassify {
            QString appType;            // 应用类型
            QStringList dependService;  // 依赖服务列表
            QString launcherDll;        // DLL文件名
            QString launcherType;       // 启动器类型
        } appClassify;
    } runEnvInfo;
};
```

### PluginManager 类

主要功能：
- `loadPlugin(appId)` - 根据appId加载插件
- `getPluginInstance()` - 获取已加载的插件实例
- `getPluginConfig()` - 获取插件配置
- `unloadPlugin()` - 卸载当前插件

## 插件加载流程

1. **解析命令行参数** - 获取appId
2. **构建配置文件路径** - `plugins/{appId}/plugin.json`
3. **读取并解析配置文件** - 使用QJsonDocument解析JSON
4. **验证配置有效性** - 检查必需字段
5. **构建DLL路径** - `plugins/{appId}/{launcherDll}`
6. **加载插件DLL** - 使用QPluginLoader
7. **创建插件实例** - 转换为IPlugin接口

## 配置文件格式

插件配置文件 `plugin.json` 示例：

```json
{
    "baseInfo": {
        "appId": "idphoto",
        "appName": "证件照",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": [
                "base",
                "network"
            ],
            "launcherDll": "idphoto.dll",
            "launcherType": "native"
        }
    }
}
```

## 目录结构

```
cfhost/
├── plugins/
│   ├── test/
│   │   └── plugin.json
│   └── idphoto/
│       └── plugin.json
└── src/
    ├── PluginConfig.h
    ├── PluginManager.h
    ├── PluginManager.cpp
    └── ...
```

## 错误处理

按照PRD要求实现"静默失败"模式：

- **配置文件不存在** - 记录日志，静默退出
- **JSON解析错误** - 记录日志，静默退出  
- **配置验证失败** - 记录日志，静默退出
- **DLL文件不存在** - 记录日志，静默退出
- **DLL加载失败** - 记录日志，静默退出
- **接口转换失败** - 记录日志，静默退出

所有错误都会通过 `qDebug()` 输出到控制台，便于开发调试。

## 测试方法

运行测试脚本：
```bash
test_plugin_loading.bat
```

测试场景：
1. 加载存在配置但无DLL的插件
2. 加载不存在的插件
3. 无appId参数的情况

## 集成说明

插件管理功能已集成到 `HostApplication` 中：

1. **初始化流程**：
   - 解析命令行参数
   - 加载插件 ← 新增步骤
   - 初始化UI

2. **生命周期管理**：
   - 应用启动时自动加载插件
   - 应用退出时自动卸载插件

## 下一步

第6步将创建实际的测试插件DLL，届时可以验证完整的插件加载流程。

## 调试信息

所有关键步骤都有详细的调试输出：
- 配置文件路径构建
- JSON解析过程
- DLL加载状态
- 插件实例创建
- 错误详细信息

通过控制台输出可以清楚地了解插件加载的每个步骤。

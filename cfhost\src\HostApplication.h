#pragma once

#include "stdafx.h"

class MainWindow;
class CommandLineArgs;
class PluginManager;
class BasicServiceProvider;

class HostApplication : public QObject
{
    Q_OBJECT

public:
    HostApplication(QObject *parent = nullptr);
    virtual ~HostApplication();

    // 初始化应用程序
    bool initialize();
    
    // 运行应用程序
    int run();

    // 获取命令行参数
    const CommandLineArgs* getCommandLineArgs() const { return m_commandLineArgs; }

    // 获取插件管理器
    PluginManager* getPluginManager() const { return m_pluginManager; }

private:
    // 解析命令行参数
    bool parseCommandLine();

    // 初始化服务提供者
    bool initializeServiceProvider();

    // 加载插件
    bool loadPlugin();

    // 初始化插件
    bool initializePlugin();

    // 初始化UI
    bool initializeUI();

private:
    MainWindow* m_mainWindow;
    CommandLineArgs* m_commandLineArgs;
    PluginManager* m_pluginManager;
    BasicServiceProvider* m_serviceProvider;
    
    // 禁用拷贝构造和赋值操作
    HostApplication(const HostApplication&) = delete;
    HostApplication& operator=(const HostApplication&) = delete;
};

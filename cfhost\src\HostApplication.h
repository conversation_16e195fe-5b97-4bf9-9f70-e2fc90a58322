#pragma once

#include "stdafx.h"

class MainWindow;
class CommandLineArgs;

class HostApplication : public QObject
{
    Q_OBJECT

public:
    HostApplication(QObject *parent = nullptr);
    virtual ~HostApplication();

    // 初始化应用程序
    bool initialize();
    
    // 运行应用程序
    int run();

    // 获取命令行参数
    const CommandLineArgs* getCommandLineArgs() const { return m_commandLineArgs; }

private:
    // 解析命令行参数
    bool parseCommandLine();

    // 初始化UI
    bool initializeUI();

private:
    MainWindow* m_mainWindow;
    CommandLineArgs* m_commandLineArgs;
    
    // 禁用拷贝构造和赋值操作
    HostApplication(const HostApplication&) = delete;
    HostApplication& operator=(const HostApplication&) = delete;
};

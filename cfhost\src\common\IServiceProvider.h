#pragma once

#include <QtCore/QString>
#include <QtCore/QSharedPointer>

class IService;

/**
 * @brief 服务提供者接口
 * 
 * 定义了服务提供者必须实现的接口，用于向插件提供服务。
 * 宿主通过实现此接口来向插件提供各种服务。
 */
class IServiceProvider
{
public:
    virtual ~IServiceProvider() = default;

    /**
     * @brief 获取指定名称的服务
     * @param serviceName 服务名称
     * @return 服务实例的智能指针，如果服务不存在则返回空指针
     */
    virtual QSharedPointer<IService> getService(const QString& serviceName) const = 0;

    /**
     * @brief 检查是否存在指定名称的服务
     * @param serviceName 服务名称
     * @return 存在返回true，否则返回false
     */
    virtual bool hasService(const QString& serviceName) const = 0;

    /**
     * @brief 获取所有可用服务的名称列表
     * @return 服务名称列表
     */
    virtual QStringList getAvailableServices() const = 0;

    /**
     * @brief 获取服务提供者的版本信息
     * @return 版本字符串
     */
    virtual QString getProviderVersion() const = 0;
};

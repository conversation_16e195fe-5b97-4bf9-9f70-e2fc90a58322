#pragma once

#include "stdafx.h"

class CommandLineArgs
{
public:
    CommandLineArgs();
    virtual ~CommandLineArgs();

    // 获取应用ID
    const QString& getAppId() const { return m_appId; }
    
    // 获取多实例模式标志
    bool isMultiInstance() const { return m_isMultiInstance; }
    
    // 获取实例ID
    const QString& getInstanceId() const { return m_instanceId; }
    
    // 设置参数值
    void setAppId(const QString& appId) { m_appId = appId; }
    void setMultiInstance(bool isMulti) { m_isMultiInstance = isMulti; }
    void setInstanceId(const QString& instanceId) { m_instanceId = instanceId; }
    
    // 验证参数有效性
    bool isValid() const;

private:
    QString m_appId;           // 应用ID（必需参数）
    bool m_isMultiInstance;    // 多实例模式标志（默认true）
    QString m_instanceId;      // 实例ID（默认"default"）
    
    // 禁用拷贝构造和赋值操作
    CommandLineArgs(const CommandLineArgs&) = delete;
    CommandLineArgs& operator=(const CommandLineArgs&) = delete;
};

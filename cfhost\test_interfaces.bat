@echo off
echo Testing core interfaces compilation...

REM 检查是否设置了QTDIR环境变量
if "%QTDIR%"=="" (
    echo Error: QTDIR environment variable is not set.
    echo Please set QTDIR to your Qt installation directory.
    echo Example: set QTDIR=C:\Qt\6.5.0\msvc2022_64
    pause
    exit /b 1
)

echo Using Qt from: %QTDIR%

echo.
echo Building project to test interface compilation...
msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:normal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Interface compilation test PASSED!
    echo All interfaces (IService, IServiceProvider, IPlugin) can be included and compiled successfully.
    echo.
    echo Interface files created:
    echo   - src\common\IService.h
    echo   - src\common\IServiceProvider.h
    echo   - src\common\IPlugin.h
    echo   - src\common\README.md
    echo.
    echo The interfaces are ready for use in plugin development.
) else (
    echo.
    echo ✗ Interface compilation test FAILED!
    echo Please check the build output for errors.
    echo.
    echo Common issues to check:
    echo 1. QTDIR environment variable is set correctly
    echo 2. Qt libraries are accessible
    echo 3. Visual Studio build tools are available
)

pause

@echo off
echo Testing core interfaces compilation...

REM 检查是否设置了QTDIR环境变量
if "%QTDIR%"=="" (
    echo Error: QTDIR environment variable is not set.
    echo Please set QTDIR to your Qt installation directory.
    echo Example: set QTDIR=C:\Qt\6.5.0\msvc2022_64
    pause
    exit /b 1
)

echo Using Qt from: %QTDIR%

echo.
echo Building project to test interface compilation...
msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Interface compilation test PASSED!
    echo All interfaces (IService, IServiceProvider, IPlugin) can be included and compiled successfully.
    echo.
    echo Interface files created:
    echo   - src\common\IService.h
    echo   - src\common\IServiceProvider.h  
    echo   - src\common\IPlugin.h
    echo   - src\common\README.md
    echo.
    echo Test implementation created:
    echo   - src\InterfaceTest.cpp
    echo.
    echo The interfaces are ready for use in plugin development.
) else (
    echo.
    echo ✗ Interface compilation test FAILED!
    echo Please check the build output for errors.
)

pause

#pragma once

#include "stdafx.h"
#include <QtNetwork/QLocalServer>
#include <QtNetwork/QLocalSocket>

class MainWindow;

class ApplicationInstance : public QObject
{
    Q_OBJECT

public:
    ApplicationInstance(QObject *parent = nullptr);
    virtual ~ApplicationInstance();

    // 检查是否已有实例运行，如果有则尝试激活
    // 返回true表示应该继续运行，false表示应该退出
    bool checkAndActivateExisting(const QString& instanceId);
    
    // 启动本地服务器监听激活请求
    bool startServer(const QString& instanceId);
    
    // 设置主窗口引用，用于激活
    void setMainWindow(MainWindow* mainWindow);

private slots:
    // 处理新的连接请求
    void onNewConnection();
    
    // 处理客户端数据
    void onClientDataReady();

private:
    // 生成服务器名称
    QString generateServerName(const QString& instanceId) const;
    
    // 尝试连接到现有实例
    bool tryConnectToExisting(const QString& serverName);

private:
    QLocalServer* m_localServer;
    MainWindow* m_mainWindow;
    QString m_instanceId;
    
    // 禁用拷贝构造和赋值操作
    ApplicationInstance(const ApplicationInstance&) = delete;
    ApplicationInstance& operator=(const ApplicationInstance&) = delete;
};

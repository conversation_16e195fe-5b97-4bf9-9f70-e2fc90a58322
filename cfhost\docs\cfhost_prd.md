# **桌面应用“宿主-插件”框架 PRD V1.0**

| **文档状态** | [草案] | **创建日期** | 2025-07-18 |
| :--- | :--- | :--- | :--- |
| **产品经理** | Cascade | **最后更新** | 2025-07-18 |

## 1. 引言

### 1.1. 项目背景
当前，在开发各类独立的图片编辑工具时，我们面临着一个普遍的挑战：每个新项目都需要重复集成一套相似的基础模块，例如会员系统、数据埋点、网络通信、文件下载等。这种重复性工作不仅消耗了大量的开发资源，也增加了维护的复杂性。

### 1.2. 目标与价值
为了解决上述问题，我们设计了一套“宿主-插件”模式的桌面应用程序框架。

*   **核心目标**：构建一个通用的、可扩展的应用程序基础平台。
*   **核心价值**：
    *   **提升效率**：将通用能力下沉到宿主程序，使插件开发者能专注于业务逻辑与UI实现，显著缩短开发周期。
    *   **提高复用**：基础模块一次开发，多处复用，保证了功能的稳定性和一致性。
    *   **降低耦合**：宿主与插件之间通过定义好的接口进行交互，实现了业务逻辑与平台能力的分离。

## 2. 核心概念定义

### 2.1. 宿主 (Host)
一个没有独立用户界面的可执行程序。它的核心职责是作为“服务容器”和“应用启动器”，负责管理应用的生命周期、加载插件、并按需向插件提供标准化的服务。

### 2.2. 插件 (Plugin)
以动态链接库（DLL）形式存在的独立模块。它负责实现具体的业务功能和用户界面（通过提供一个 `QWidget`），并通过调用宿主提供的服务来完成其工作。

### 2.3. 服务 (Service)
由宿主提供的特定能力或功能，例如网络请求服务、用户账户服务等。
*   **服务模型**：服务之间相互独立，不存在依赖关系，也暂不考虑版本管理，以简化框架设计。
*   **服务注册**：宿主会注册一套基础服务，并根据插件配置文件的`dependService`字段，按需注册可选服务。

### 2.4. 插件配置文件 (plugin.json)
每个插件都附带一个JSON格式的配置文件，用于向宿主描述其基本信息、依赖和入口点。

**配置示例:**
```json
{
    "baseInfo": {
        "appId": "idphoto",
        "appName": "证件照",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": [
                "base" 
            ],
            "launcherDll": "idphoto.dll",
            "launcherType": "native"
        }
    }
}
```

## 3. 功能需求详述

### 3.1. 启动流程 (命令行解析)
宿主程序通过解析命令行参数来确定要加载的插件和运行模式。

*   **支持参数**:
    *   `/appId:{plugin_id}`: **（必需）** 指定要加载的插件ID。
    *   `/multi:{0|1}`: 指定实例模式。`1`为多实例（默认），`0`为单实例。
    *   `/instanceId:{instance_id}`: 指定进程的实例ID，用于实现单实例激活逻辑。
*   **实现说明**: 参数的解析和存储封装在独立的类中，可使用`CCmdLineParser`类。

### 3.2. 实例管理 (单实例/多实例模式)
*   **多实例模式 (`/multi:1`)**: 默认模式，每次启动都会创建一个新的应用程序进程。
*   **单实例模式 (`/multi:0`)**:
    *   **启动检查**: 当以单实例模式启动时，程序会检查是否已存在一个以单实例模式运行、且`instanceId`相同的进程。
    *   **激活逻辑**: 如果检查到已存在符合条件的进程，新进程会通知旧进程将其窗口带到前台，然后新进程自行退出。
    *   **简化处理**: 暂不考虑旧进程因卡死而无法响应激活请求的异常情况。

### 3.3. 插件加载与初始化流程
1.  **解析参数**: 宿主启动，解析命令行参数，获取`appId`。
2.  **读取配置**: 根据`appId`找到并读取对应的`plugin.json`配置文件，并将其内容解析到结构体中。
3.  **注册服务**: 宿主初始化基础服务，然后根据配置文件中的`dependService`列表，注册插件所需的可选服务。
4.  **加载插件**: 宿主使用Qt Plugin机制，加载配置文件中`launcherDll`指定的插件DLL。
5.  **注入与初始化**: 宿主将注册好的服务注入到插件实例中，并调用插件的初始化方法。
6.  **显示界面**: 宿主获取插件提供的`QWidget`，并将其填充到主窗口的内容区进行显示。

### 3.4. 主窗口行为
*   **窗口结构**: 主窗口由标题栏和内容区组成。
*   **窗口标题**: 自动读取并显示插件配置文件中的`appName`字段。
*   **内容填充**: 内容区完全由插件提供的`QWidget`填充。

### 3.5. 宿主与插件交互接口
交互是严格单向的，插件不能反向控制宿主，唯一的例外是关闭确认。

*   **服务调用 (Host -> Plugin)**: 插件可以调用宿主提供的服务接口，但无需关心其内部实现。
*   **关闭确认 (Host -> Plugin)**:
    1.  当用户点击主窗口的关闭按钮时，宿主会向插件发送一个“是否可以关闭”的询问。
    2.  插件根据自身状态（如是否有未保存的工作）返回`true`（可以关闭）或`false`（不能关闭）。
    3.  如果返回`true`，宿主关闭进程。
    4.  如果返回`false`，宿主不执行任何操作。**此时，由插件负责通过UI（如弹窗）向用户解释无法关闭的原因。**

### 3.6. 错误处理与日志记录
*   **策略**: 采用“静默失败”模式，以保证用户体验的简洁，同时方便开发者排错。
*   **触发条件**: 在启动阶段发生任何关键错误，包括：
    *   命令行未提供`/appId`参数。
    *   根据`appId`找不到配置文件，或配置文件格式错误、缺少关键字段。
    *   插件DLL文件不存在，或因依赖、损坏等原因加载失败。
*   **处理方式**: 程序不显示任何错误对话框，直接退出。同时，将详细的错误信息记录到本地日志文件中。

## 4. 非功能需求

### 4.1. 技术栈要求
*   优先使用Qt的类库，包括但不限于集合类（`QVector`, `QMap`等）、智能指针（`QSharedPointer`等）和基础数据类型。

### 4.2. 编码规范
*   遵循团队C++编码规范。
*   逻辑封装：任何包含超过两个功能点的逻辑单元，都必须进行封装，提炼为独立的方法或类，以保证代码的清晰度和可维护性。

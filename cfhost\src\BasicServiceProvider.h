#pragma once

#include "stdafx.h"
#include "common/IServiceProvider.h"

/**
 * @brief 基础服务提供者实现
 * 
 * 为插件提供基本的服务提供者功能。
 * 这是一个简单的实现，主要用于测试插件框架。
 */
class BasicServiceProvider : public QObject, public IServiceProvider
{
    Q_OBJECT

public:
    BasicServiceProvider(QObject *parent = nullptr);
    virtual ~BasicServiceProvider();

    // IServiceProvider 接口实现
    QSharedPointer<IService> getService(const QString& serviceName) const override;
    bool hasService(const QString& serviceName) const override;
    QStringList getAvailableServices() const override;
    QString getProviderVersion() const override;

private:
    // 禁用拷贝构造和赋值操作
    BasicServiceProvider(const BasicServiceProvider&) = delete;
    BasicServiceProvider& operator=(const BasicServiceProvider&) = delete;
};

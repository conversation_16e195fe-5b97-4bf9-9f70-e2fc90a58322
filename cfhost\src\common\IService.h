#pragma once

#include <QtCore/QString>

/**
 * @brief 所有服务类的基接口
 * 
 * 这是一个纯虚基类，定义了所有服务必须实现的基本接口。
 * 服务之间相互独立，不存在依赖关系，也暂不考虑版本管理。
 */
class IService
{
public:
    virtual ~IService() = default;

    /**
     * @brief 获取服务名称
     * @return 服务的唯一标识名称
     */
    virtual QString getServiceName() const = 0;

    /**
     * @brief 获取服务版本
     * @return 服务版本字符串
     */
    virtual QString getServiceVersion() const = 0;

    /**
     * @brief 初始化服务
     * @return 初始化成功返回true，失败返回false
     */
    virtual bool initialize() = 0;

    /**
     * @brief 清理服务资源
     */
    virtual void cleanup() = 0;

    /**
     * @brief 检查服务是否已初始化
     * @return 已初始化返回true，否则返回false
     */
    virtual bool isInitialized() const = 0;
};

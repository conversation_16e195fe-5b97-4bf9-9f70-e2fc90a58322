#pragma once

// Windows 头文件
#include <windows.h>
#include <atlstr.h>

// Qt 头文件
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtCore/QDebug>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QObject>
#include <QtNetwork/QLocalServer>
#include <QtNetwork/QLocalSocket>

// 标准库头文件
#include <memory>
#include <map>
#include <string>

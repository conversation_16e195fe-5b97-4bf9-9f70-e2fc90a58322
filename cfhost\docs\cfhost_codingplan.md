# **“宿主-插件”框架开发计划**

本文档根据产品需求文档（PRD）制定了详细的、渐进式的开发步骤，旨在确保项目开发的稳定性、可控性和可验证性。

## 1. 拟定代码目录结构

为了适配 Visual Studio 2022 的项目结构并实现模块化，拟定目录结构如下：

```
cfhost/
├── cfhost.sln              # Visual Studio 解决方案文件
├── cfhost.vcxproj          # Visual Studio 项目文件
│
├── src/
│   ├── common/             # 共享接口文件夹
│   │   ├── IPlugin.h           # 插件必须实现的接口
│   │   ├── IService.h          # 所有服务实现的基类接口
│   │   └── IServiceProvider.h  # 服务提供者接口
│   │
│   ├── main.cpp              # 应用程序入口 (保持简洁)
│   ├── HostApplication.h     # (新增) 应用程序核心管理类
│   ├── HostApplication.cpp   # (新增)
│   ├── MainWindow.h          # 主窗口
│   ├── MainWindow.cpp
│   ├── CmdLineParser.h       # (已存在) 命令行参数解析器
│   ├── CmdLineParser.cpp     # (已存在)
│   ├── CommandLineArgs.h     # 命令行参数存储类
│   ├── CommandLineArgs.cpp
│   ├── ApplicationInstance.h # 单实例逻辑控制器
│   ├── ApplicationInstance.cpp
│   ├── PluginManager.h       # 插件加载与管理
│   ├── PluginManager.cpp
│   ├── ServiceManager.h      # 服务注册与管理
│   └── ServiceManager.cpp
│
├── plugins/
│   └── idphoto/              # 示例插件目录
│       ├── idphoto.dll
│       └── plugin.json
│
└── docs/
    └── cfhost_codingplan.md
```

## 2. 受影响的现有模块

这是一个新项目，因此没有需要适配的现有模块。

## 3. 渐进式小步迭代开发步骤

我们将按照以下步骤进行开发，每一步都将产出一个可运行和验证的程序版本。

### **第1步：创建应用程序核心类与基础主窗口**

- **目标**：创建 `HostApplication` 类来封装和管理应用程序的生命周期，并显示一个空白主窗口。
- **任务**：
    1.  创建 `HostApplication` 类，它将负责初始化应用、处理核心逻辑和启动UI。
    2.  创建 `MainWindow` 类，继承自 `QMainWindow`。
    3.  在 `HostApplication` 的初始化方法中，创建并显示 `MainWindow` 实例。
    4.  简化 `main.cpp`，使其只包含：创建 `QApplication` 和 `HostApplication` 实例，然后调用 `HostApplication` 的运行方法并进入事件循环。
- **验证**：编译并运行 `cfhost.exe`，应能看到一个空白的桌面窗口弹出。

### **第2步：集成命令行参数解析与存储**

- **目标**：集成 `CmdLineParser` 并创建一个新类来统一存储和管理命令行参数。
- **任务**：
    1.  创建 `CommandLineArgs` 类，用于存储解析后的命令行参数（如 `appId`, `multi`, `instanceId`），并提供访问这些参数的接口。
    2.  在 `HostApplication` 的初始化流程中，实例化 `CmdLineParser` 来解析 `QApplication::arguments()`。
    3.  将解析出的参数值存入一个 `CommandLineArgs` 的实例中，该实例由 `HostApplication` 持有。
    4.  应用程序的其他部分通过访问 `HostApplication` 提供的接口来获取 `CommandLineArgs` 实例，实现统一管理。
- **验证**：
    -   运行 `cfhost.exe /appId:idphoto /multi:0 /instanceId:app_instance_1`。
    -   通过日志或 `qDebug()` 打印从 `CommandLineArgs` 实例中获取的值，确认所有参数均被正确存储和访问。

### **第3步：实现单实例/多实例模式**

- **目标**：根据 `/multi` 参数，控制应用程序只能启动单个或多个实例。
- **任务**：
    1.  创建 `ApplicationInstance` 类，使用 `QLocalServer` 和 `QLocalSocket` 实现单实例检测和激活逻辑。
    2.  在 `HostApplication` 的初始化流程中，根据 `CommandLineArgs` 的配置决定是否执行单实例检查。
    3.  `HostApplication` 持有并调用 `ApplicationInstance` 实例来检查。如果已有实例，则 `HostApplication` 负责退出当前进程；否则，继续执行启动流程。
    4.  `MainWindow` 需提供一个 `activateWindow()` 方法，`HostApplication` 在收到激活消息后调用该方法将窗口置于顶层。
- **验证**：
    1.  运行 `cfhost.exe /appId:idphoto /multi:0`，窗口显示。
    2.  再次运行相同命令，新进程应立即退出，同时第一个窗口被激活（闪烁或置顶）。
    3.  运行 `cfhost.exe /appId:idphoto /multi:1` 两次，应出现两个独立的窗口。

### **第4步：定义核心接口**

- **目标**：定义宿主与插件之间的契约，使它们可以解耦。
- **任务**：
    1.  在 `src` 目录下创建 `common` 文件夹，用于存放共享的接口定义文件。在 Visual Studio 工程中，可以为此文件夹创建一个筛选器（Filter）进行组织。
    2.  在 `common` 文件夹中创建以下纯虚基类（接口）：
        -   `IService`：所有服务类的基接口。
        -   `IServiceProvider`：服务提供者接口，定义 `getService()` 方法。
        -   `IPlugin`：插件必须实现的接口，定义 `init(IServiceProvider*)`、`getWidget()` 和 `canClose()` 等方法。
- **验证**：接口头文件可以被 `cfhost` 项目中的其他源文件正常 `#include` 并通过编译。

### **第5步：实现插件加载与管理**

- **目标**：宿主能够根据 `appId` 查找、读取配置并加载插件DLL。
- **任务**：
    1.  创建 `PluginConfig` 结构体，用于存储从 `plugin.json` 解析的数据。
    2.  创建 `PluginManager` 类，负责：
        -   根据 `appId` 构建 `plugin.json` 的路径（例如 `plugins/{appId}/plugin.json`）。
        -   读取并使用 `QJsonDocument` 解析 `plugin.json` 文件。
        -   使用 `QPluginLoader` 加载 `launcherDll` 指定的插件文件。
        -   实例化插件，并进行类型转换为 `IPlugin*`。
    3.  在 `main.cpp` 中，调用 `PluginManager` 完成插件加载。
    4.  实现PRD要求的错误处理：任何加载步骤失败，都记录日志并静默退出。
- **验证**：
    -   需要创建一个最简的测试插件（见第6步）并放置在 `plugins/test` 目录下。
    -   运行 `cfhost.exe /appId:test`，通过日志确认插件加载成功。
    -   通过删除 `plugin.json` 或 DLL 文件，验证程序是否能按预期静默退出并记录错误。

### **第6步：创建最小可行性测试插件**

- **目标**：创建一个能与宿主交互的、最简单的插件。
- **任务**：
    1.  创建一个新的DLL项目 `test_plugin`。
    2.  引用 `cfcommon` 模块的接口定义。
    3.  创建一个类，继承 `QObject` 和 `IPlugin`。
    4.  实现 `IPlugin` 的所有接口：
        -   `init()`: 暂时为空。
        -   `getWidget()`: 返回一个包含 `QLabel` 的简单 `QWidget`，标签文本为“插件加载成功”。
        -   `canClose()`: 直接返回 `true`。
    5.  使用 `Q_INTERFACES` 和 `Q_PLUGIN_METADATA` 宏导出插件。
- **验证**：将编译好的 `test_plugin.dll` 和对应的 `plugin.json` 放入 `plugins/test` 目录。宿主程序在第7步完成后应能成功加载并显示此插件的UI。

### **第7步：集成插件UI到主窗口**

- **目标**：将已加载插件的UI控件显示在主窗口中。
- **任务**：
    1.  在 `MainWindow` 中，从 `PluginManager` 获取加载的 `IPlugin` 实例。
    2.  调用插件的 `getWidget()` 方法，获取其UI控件。
    3.  将返回的 `QWidget*` 设置为 `MainWindow` 的中央控件 (`setCentralWidget`)。
    4.  从插件的配置中读取 `appName`，并设置为主窗口的标题。
- **验证**：运行 `cfhost.exe /appId:test`，主窗口标题应为测试插件的名称，内容区应显示“插件加载成功”的标签。

### **第8步：实现服务管理与注入**

- **目标**：建立一个基本的服务注册和提供机制。
- **任务**：
    1.  创建 `ServiceManager` 类，实现 `IServiceProvider` 接口。
    2.  `ServiceManager` 内部使用 `QMap<QString, QSharedPointer<IService>>` 来存储服务实例。
    3.  提供 `registerService()` 方法。
    4.  在宿主启动时，实例化 `ServiceManager` 并注册一个或多个基础服务（可先用空实现的服务占位）。
    5.  修改 `PluginManager`，在加载插件后，调用插件的 `init()` 方法，并将 `ServiceManager` 实例作为 `IServiceProvider*` 传入。
- **验证**：在测试插件的 `init()` 方法中，尝试从传入的 `IServiceProvider` 获取服务，并通过日志确认服务获取成功。

### **第9步：实现窗口关闭确认逻辑**

- **目标**：在用户关闭窗口时，宿主能够征求插件的意见。
- **任务**：
    1.  重写 `MainWindow` 的 `closeEvent()` 方法。
    2.  在 `closeEvent()` 中，调用 `IPlugin` 实例的 `canClose()` 方法。
    3.  如果 `canClose()` 返回 `true`，则接受关闭事件，窗口正常关闭。
    4.  如果 `canClose()` 返回 `false`，则忽略该事件，窗口保持打开。
- **验证**：
    -   修改测试插件，使其 `canClose()` 方法返回 `false`。运行宿主并尝试关闭窗口，窗口应无法关闭。
    -   修改 `canClose()` 返回 `true`，窗口应能正常关闭。

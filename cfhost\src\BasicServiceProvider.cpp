#include "stdafx.h"
#include "BasicServiceProvider.h"

BasicServiceProvider::BasicServiceProvider(QObject *parent)
    : QObject(parent)
{
    qDebug() << "BasicServiceProvider created";
}

BasicServiceProvider::~BasicServiceProvider()
{
    qDebug() << "BasicServiceProvider destroyed";
}

QSharedPointer<IService> BasicServiceProvider::getService(const QString& serviceName) const
{
    qDebug() << "BasicServiceProvider::getService called with serviceName:" << serviceName;
    
    // 目前没有实际的服务实现，返回空指针
    // 在后续步骤中，这里会返回实际的服务实例
    Q_UNUSED(serviceName);
    return QSharedPointer<IService>();
}

bool BasicServiceProvider::hasService(const QString& serviceName) const
{
    qDebug() << "BasicServiceProvider::hasService called with serviceName:" << serviceName;
    
    // 目前没有实际的服务，都返回false
    // 在后续步骤中，这里会检查实际的服务注册表
    Q_UNUSED(serviceName);
    return false;
}

QStringList BasicServiceProvider::getAvailableServices() const
{
    qDebug() << "BasicServiceProvider::getAvailableServices called";
    
    // 返回一个模拟的服务列表用于测试
    QStringList services;
    services << "base" << "ui" << "config";
    
    return services;
}

QString BasicServiceProvider::getProviderVersion() const
{
    return "1.0.0";
}

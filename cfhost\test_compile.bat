@echo off
echo Testing project compilation...

REM 检查是否设置了QTDIR环境变量
if "%QTDIR%"=="" (
    echo Error: QTDIR environment variable is not set.
    echo Please set QTDIR to your Qt installation directory.
    echo Example: set QTDIR=C:\Qt\6.5.0\msvc2022_64
    pause
    exit /b 1
)

echo Using Qt from: %QTDIR%

echo.
echo Attempting to build the project...
echo This will test if all interfaces and core classes compile correctly.

msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:normal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ COMPILATION SUCCESSFUL!
    echo.
    echo All core interfaces and classes compiled successfully:
    echo   - IService interface
    echo   - IServiceProvider interface  
    echo   - IPlugin interface
    echo   - HostApplication class
    echo   - CommandLineArgs class
    echo   - MainWindow class
    echo   - ApplicationInstance class
    echo.
    echo The project is ready for the next development step.
) else (
    echo.
    echo ✗ COMPILATION FAILED!
    echo Please check the build output above for specific errors.
    echo.
    echo Common issues to check:
    echo 1. QTDIR environment variable points to correct Qt installation
    echo 2. Visual Studio 2022 is installed with C++ tools
    echo 3. Qt6 libraries are available and compatible
    echo 4. All source files are present and syntactically correct
)

pause

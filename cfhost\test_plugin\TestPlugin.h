#pragma once

#include "stdafx.h"

/**
 * @brief 测试插件类
 * 
 * 这是一个最小可行性测试插件，用于验证宿主-插件框架的功能。
 * 实现了IPlugin接口的所有方法，提供基本的插件功能。
 */
class TestPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)
    Q_PLUGIN_METADATA(IID "com.cfpixelengine.cfhost.IPlugin" FILE "plugin.json")

public:
    TestPlugin(QObject *parent = nullptr);
    virtual ~TestPlugin();

    // IPlugin 接口实现
    bool init(IServiceProvider* serviceProvider) override;
    QWidget* getWidget() override;
    bool canClose() override;
    QString getPluginName() const override;
    QString getPluginVersion() const override;
    QString getPluginDescription() const override;
    void cleanup() override;

private slots:
    // 按钮点击事件处理
    void onButtonClicked();

private:
    // 创建插件UI
    void createUI();

private:
    IServiceProvider* m_serviceProvider;    // 服务提供者
    QWidget* m_mainWidget;                  // 主UI控件
    QPushButton* m_testButton;              // 测试按钮
    QLabel* m_statusLabel;                  // 状态标签
    QTextEdit* m_logTextEdit;               // 日志文本框
    bool m_initialized;                     // 初始化状态

    // 禁用拷贝构造和赋值操作
    TestPlugin(const TestPlugin&) = delete;
    TestPlugin& operator=(const TestPlugin&) = delete;
};

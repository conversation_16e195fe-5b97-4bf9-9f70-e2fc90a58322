#include "stdafx.h"
#include "PluginManager.h"
#include "common/IPlugin.h"

PluginManager::PluginManager(QObject *parent)
    : QObject(parent)
    , m_pluginLoader(nullptr)
    , m_pluginInstance(nullptr)
{
    qDebug() << "PluginManager created";
}

PluginManager::~PluginManager()
{
    unloadPlugin();
    qDebug() << "PluginManager destroyed";
}

QString PluginManager::buildConfigPath(const QString& appId) const
{
    // 构建配置文件路径：plugins/{appId}/plugin.json
    QDir pluginsDir("plugins");
    QDir appDir = pluginsDir.absoluteFilePath(appId);
    QString configPath = appDir.absoluteFilePath("plugin.json");
    
    qDebug() << "Config path for appId" << appId << ":" << configPath;
    return configPath;
}

QString PluginManager::buildDllPath(const QString& appId, const QString& dllFileName) const
{
    // 构建DLL文件路径：plugins/{appId}/{dllFileName}
    QDir pluginsDir("plugins");
    QDir appDir = pluginsDir.absoluteFilePath(appId);
    QString dllPath = appDir.absoluteFilePath(dllFileName);
    
    qDebug() << "DLL path for appId" << appId << ":" << dllPath;
    return dllPath;
}

bool PluginManager::logErrorAndFail(const QString& message)
{
    qDebug() << "PluginManager Error:" << message;
    // TODO: 在后续步骤中，这里应该写入到日志文件
    return false;
}

bool PluginManager::parseConfigFile(const QString& configPath)
{
    qDebug() << "Parsing config file:" << configPath;
    
    // 检查文件是否存在
    QFile configFile(configPath);
    if (!configFile.exists()) {
        return logErrorAndFail(QString("Config file does not exist: %1").arg(configPath));
    }
    
    // 打开文件
    if (!configFile.open(QIODevice::ReadOnly)) {
        return logErrorAndFail(QString("Cannot open config file: %1").arg(configPath));
    }
    
    // 读取文件内容
    QByteArray configData = configFile.readAll();
    configFile.close();
    
    // 解析JSON
    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(configData, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        return logErrorAndFail(QString("JSON parse error in %1: %2").arg(configPath, parseError.errorString()));
    }
    
    if (!jsonDoc.isObject()) {
        return logErrorAndFail(QString("Config file is not a JSON object: %1").arg(configPath));
    }
    
    QJsonObject rootObj = jsonDoc.object();
    
    // 解析baseInfo
    if (!rootObj.contains("baseInfo") || !rootObj["baseInfo"].isObject()) {
        return logErrorAndFail("Missing or invalid 'baseInfo' section in config");
    }
    
    QJsonObject baseInfoObj = rootObj["baseInfo"].toObject();
    m_pluginConfig.baseInfo.appId = baseInfoObj["appId"].toString();
    m_pluginConfig.baseInfo.appName = baseInfoObj["appName"].toString();
    m_pluginConfig.baseInfo.buildVersion = baseInfoObj["buildVersion"].toInt();
    
    // 解析runEnvInfo
    if (!rootObj.contains("runEnvInfo") || !rootObj["runEnvInfo"].isObject()) {
        return logErrorAndFail("Missing or invalid 'runEnvInfo' section in config");
    }
    
    QJsonObject runEnvInfoObj = rootObj["runEnvInfo"].toObject();
    
    if (!runEnvInfoObj.contains("appClassify") || !runEnvInfoObj["appClassify"].isObject()) {
        return logErrorAndFail("Missing or invalid 'appClassify' section in config");
    }
    
    QJsonObject appClassifyObj = runEnvInfoObj["appClassify"].toObject();
    m_pluginConfig.runEnvInfo.appClassify.appType = appClassifyObj["appType"].toString();
    m_pluginConfig.runEnvInfo.appClassify.launcherDll = appClassifyObj["launcherDll"].toString();
    m_pluginConfig.runEnvInfo.appClassify.launcherType = appClassifyObj["launcherType"].toString();
    
    // 解析dependService数组
    if (appClassifyObj.contains("dependService") && appClassifyObj["dependService"].isArray()) {
        QJsonArray dependServiceArray = appClassifyObj["dependService"].toArray();
        for (const QJsonValue& value : dependServiceArray) {
            if (value.isString()) {
                m_pluginConfig.runEnvInfo.appClassify.dependService.append(value.toString());
            }
        }
    }
    
    qDebug() << "Config parsed successfully:" << m_pluginConfig.getSummary();
    
    // 验证配置
    if (!m_pluginConfig.isValid()) {
        return logErrorAndFail("Invalid plugin configuration");
    }
    
    return true;
}

bool PluginManager::loadPluginDll(const QString& dllPath)
{
    qDebug() << "Loading plugin DLL:" << dllPath;
    
    // 检查DLL文件是否存在
    if (!QFile::exists(dllPath)) {
        return logErrorAndFail(QString("Plugin DLL does not exist: %1").arg(dllPath));
    }
    
    // 创建插件加载器
    m_pluginLoader = new QPluginLoader(dllPath, this);
    
    // 加载插件
    if (!m_pluginLoader->load()) {
        QString errorMsg = QString("Failed to load plugin DLL: %1. Error: %2")
                          .arg(dllPath, m_pluginLoader->errorString());
        delete m_pluginLoader;
        m_pluginLoader = nullptr;
        return logErrorAndFail(errorMsg);
    }
    
    // 获取插件实例
    QObject* pluginObject = m_pluginLoader->instance();
    if (!pluginObject) {
        QString errorMsg = QString("Failed to get plugin instance from: %1").arg(dllPath);
        m_pluginLoader->unload();
        delete m_pluginLoader;
        m_pluginLoader = nullptr;
        return logErrorAndFail(errorMsg);
    }
    
    // 转换为IPlugin接口
    m_pluginInstance = qobject_cast<IPlugin*>(pluginObject);
    if (!m_pluginInstance) {
        QString errorMsg = QString("Plugin does not implement IPlugin interface: %1").arg(dllPath);
        m_pluginLoader->unload();
        delete m_pluginLoader;
        m_pluginLoader = nullptr;
        return logErrorAndFail(errorMsg);
    }
    
    qDebug() << "Plugin DLL loaded successfully:" << dllPath;
    return true;
}

bool PluginManager::loadPlugin(const QString& appId)
{
    qDebug() << "Loading plugin with appId:" << appId;
    
    // 如果已经加载了插件，先卸载
    if (isPluginLoaded()) {
        qDebug() << "Unloading existing plugin before loading new one";
        unloadPlugin();
    }
    
    // 重置配置
    m_pluginConfig = PluginConfig();
    m_currentAppId = appId;
    
    // 步骤1：构建配置文件路径
    QString configPath = buildConfigPath(appId);
    
    // 步骤2：解析配置文件
    if (!parseConfigFile(configPath)) {
        return false; // 错误已在parseConfigFile中记录
    }
    
    // 步骤3：构建DLL路径
    QString dllPath = buildDllPath(appId, m_pluginConfig.runEnvInfo.appClassify.launcherDll);
    
    // 步骤4：加载插件DLL
    if (!loadPluginDll(dllPath)) {
        return false; // 错误已在loadPluginDll中记录
    }
    
    qDebug() << "Plugin loaded successfully:" << appId;
    return true;
}

void PluginManager::unloadPlugin()
{
    if (m_pluginInstance) {
        qDebug() << "Cleaning up plugin instance";
        m_pluginInstance->cleanup();
        m_pluginInstance = nullptr;
    }
    
    if (m_pluginLoader) {
        qDebug() << "Unloading plugin DLL";
        m_pluginLoader->unload();
        delete m_pluginLoader;
        m_pluginLoader = nullptr;
    }
    
    m_pluginConfig = PluginConfig();
    m_currentAppId.clear();
    
    qDebug() << "Plugin unloaded";
}

#pragma once

#include <QtCore/QString>
#include <QtWidgets/QWidget>

class IServiceProvider;

/**
 * @brief 插件必须实现的接口
 * 
 * 这是插件与宿主之间的核心契约，定义了插件必须实现的所有方法。
 * 插件通过实现此接口来与宿主进行交互。
 */
class IPlugin
{
public:
    virtual ~IPlugin() = default;

    /**
     * @brief 初始化插件
     * @param serviceProvider 服务提供者，插件可通过此接口获取宿主提供的服务
     * @return 初始化成功返回true，失败返回false
     */
    virtual bool init(IServiceProvider* serviceProvider) = 0;

    /**
     * @brief 获取插件的UI控件
     * @return 插件的主UI控件，宿主将此控件显示在主窗口中
     */
    virtual QWidget* getWidget() = 0;

    /**
     * @brief 检查插件是否可以关闭
     * @return 可以关闭返回true，不能关闭返回false
     * 
     * 当用户尝试关闭主窗口时，宿主会调用此方法询问插件是否可以关闭。
     * 如果插件有未保存的工作或正在执行重要操作，应返回false并通过UI
     * 向用户说明无法关闭的原因。
     */
    virtual bool canClose() = 0;

    /**
     * @brief 获取插件名称
     * @return 插件的显示名称
     */
    virtual QString getPluginName() const = 0;

    /**
     * @brief 获取插件版本
     * @return 插件版本字符串
     */
    virtual QString getPluginVersion() const = 0;

    /**
     * @brief 获取插件描述
     * @return 插件的详细描述
     */
    virtual QString getPluginDescription() const = 0;

    /**
     * @brief 清理插件资源
     * 
     * 在插件被卸载前调用，插件应在此方法中清理所有资源。
     */
    virtual void cleanup() = 0;
};

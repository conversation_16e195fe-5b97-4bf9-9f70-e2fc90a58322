#include "stdafx.h"
#include "MainWindow.h"
#include "CmdLineParser.h"
#include "ApplicationInstance.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序基本信息
    app.setApplicationName("CFHost");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("CFPixelEngine");

    qDebug() << "CFHost application starting...";

    // 解析命令行参数
    QStringList arguments = app.arguments();
    QString cmdLine;

    // 跳过第一个参数（程序名），拼接其余参数
    for (int i = 1; i < arguments.size(); ++i) {
        if (i > 1) cmdLine += " ";
        cmdLine += arguments[i];
    }

    qDebug() << "Command line arguments:" << cmdLine;

    // 使用CmdLineParser解析参数
    CCmdLineParser parser;
    QString appIdStr;
    bool isMultiInstance = true; // 默认为多实例模式
    QString instanceIdStr = "default";

    if (!cmdLine.isEmpty()) {
        // 将QString转换为LPCTSTR
        std::wstring wstr = cmdLine.toStdWString();
        parser.Parse(wstr.c_str());

        // 输出解析结果用于验证
        qDebug() << "Parsed command line parameters:";
        CCmdLineParser::POSITION pos = parser.getFirst();
        CCmdLineParser_String sKey, sValue;
        while (!parser.isLast(pos)) {
            parser.getNext(pos, sKey, sValue);
            QString key = QString::fromStdWString(sKey.GetString());
            QString value = QString::fromStdWString(sValue.GetString());
            qDebug() << "  Key:" << key << "Value:" << value;
        }

        // 检查必需的appId参数
        if (parser.HasKey(L"appId")) {
            LPCTSTR appId = parser.GetVal(L"appId");
            appIdStr = QString::fromStdWString(appId);
            qDebug() << "AppId:" << appIdStr;
        } else {
            qDebug() << "Warning: No appId parameter provided";
        }

        // 检查multi参数
        if (parser.HasKey(L"multi")) {
            LPCTSTR multi = parser.GetVal(L"multi");
            QString multiStr = QString::fromStdWString(multi);
            isMultiInstance = (multiStr != "0");
            qDebug() << "Multi mode:" << (isMultiInstance ? "enabled" : "disabled");
        } else {
            qDebug() << "Multi mode: default (enabled)";
        }

        // 检查instanceId参数
        if (parser.HasKey(L"instanceId")) {
            LPCTSTR instanceId = parser.GetVal(L"instanceId");
            instanceIdStr = QString::fromStdWString(instanceId);
            qDebug() << "Instance ID:" << instanceIdStr;
        }
    }

    // 实例管理逻辑
    ApplicationInstance* appInstance = nullptr;
    if (!isMultiInstance) {
        qDebug() << "Single instance mode enabled";
        appInstance = new ApplicationInstance(&app);

        // 检查是否已有实例运行
        if (!appInstance->checkAndActivateExisting(instanceIdStr)) {
            qDebug() << "Existing instance found and activated, exiting";
            delete appInstance;
            return 0;
        }

        // 启动本地服务器监听激活请求
        if (!appInstance->startServer(instanceIdStr)) {
            qDebug() << "Failed to start instance server";
            delete appInstance;
            return 1;
        }
    } else {
        qDebug() << "Multi instance mode enabled";
    }

    // 创建并显示主窗口
    MainWindow window;

    // 如果是单实例模式，设置主窗口引用
    if (appInstance) {
        appInstance->setMainWindow(&window);
    }

    window.show();

    qDebug() << "MainWindow displayed, entering event loop";

    int result = app.exec();

    // 清理资源
    if (appInstance) {
        delete appInstance;
    }

    return result;
}

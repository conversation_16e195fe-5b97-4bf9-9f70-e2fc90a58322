#include "stdafx.h"
#include "HostApplication.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序基本信息
    app.setApplicationName("CFHost");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("CFPixelEngine");

    qDebug() << "CFHost application starting...";

    // 创建HostApplication实例
    HostApplication hostApp;

    // 初始化应用程序
    if (!hostApp.initialize()) {
        qDebug() << "Failed to initialize HostApplication";
        return 1;
    }

    // 运行应用程序
    return hostApp.run();
}

#include "stdafx.h"
#include "TestPlugin.h"

TestPlugin::TestPlugin(QObject *parent)
    : QObject(parent)
    , m_serviceProvider(nullptr)
    , m_mainWidget(nullptr)
    , m_test<PERSON>utton(nullptr)
    , m_status<PERSON>abel(nullptr)
    , m_logTextEdit(nullptr)
    , m_initialized(false)
{
    qDebug() << "TestPlugin created";
}

TestPlugin::~TestPlugin()
{
    cleanup();
    qDebug() << "TestPlugin destroyed";
}

bool TestPlugin::init(IServiceProvider* serviceProvider)
{
    qDebug() << "TestPlugin::init called";
    
    if (m_initialized) {
        qDebug() << "TestPlugin already initialized";
        return true;
    }
    
    if (!serviceProvider) {
        qDebug() << "TestPlugin::init - serviceProvider is null";
        return false;
    }
    
    m_serviceProvider = serviceProvider;
    
    // 创建UI
    createUI();
    
    // 记录可用服务
    QStringList availableServices = m_serviceProvider->getAvailableServices();
    QString servicesText = QString("Available services: %1").arg(availableServices.join(", "));
    m_logTextEdit->append(servicesText);
    qDebug() << servicesText;
    
    m_initialized = true;
    m_statusLabel->setText("Status: Initialized");
    m_logTextEdit->append("Plugin initialized successfully");
    
    qDebug() << "TestPlugin initialized successfully";
    return true;
}

void TestPlugin::createUI()
{
    qDebug() << "TestPlugin::createUI called";
    
    // 创建主控件
    m_mainWidget = new QWidget();
    m_mainWidget->setWindowTitle("Test Plugin");
    m_mainWidget->resize(400, 300);
    
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(m_mainWidget);
    
    // 创建标题标签
    QLabel* titleLabel = new QLabel("CFHost Test Plugin");
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
    layout->addWidget(titleLabel);
    
    // 创建状态标签
    m_statusLabel = new QLabel("Status: Not initialized");
    m_statusLabel->setStyleSheet("color: blue; margin: 5px;");
    layout->addWidget(m_statusLabel);
    
    // 创建测试按钮
    m_testButton = new QPushButton("Test Plugin Function");
    connect(m_testButton, &QPushButton::clicked, this, &TestPlugin::onButtonClicked);
    layout->addWidget(m_testButton);
    
    // 创建日志文本框
    QLabel* logLabel = new QLabel("Plugin Log:");
    layout->addWidget(logLabel);
    
    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setMaximumHeight(150);
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->append("Plugin UI created");
    layout->addWidget(m_logTextEdit);
    
    // 添加版本信息
    QString versionInfo = QString("Plugin: %1 v%2").arg(getPluginName(), getPluginVersion());
    QLabel* versionLabel = new QLabel(versionInfo);
    versionLabel->setStyleSheet("color: gray; font-size: 10px;");
    versionLabel->setAlignment(Qt::AlignCenter);
    layout->addWidget(versionLabel);
    
    qDebug() << "TestPlugin UI created successfully";
}

QWidget* TestPlugin::getWidget()
{
    qDebug() << "TestPlugin::getWidget called";
    return m_mainWidget;
}

bool TestPlugin::canClose()
{
    qDebug() << "TestPlugin::canClose called";
    
    // 对于测试插件，总是允许关闭
    // 在实际插件中，这里可能需要检查是否有未保存的工作
    return true;
}

QString TestPlugin::getPluginName() const
{
    return "Test Plugin";
}

QString TestPlugin::getPluginVersion() const
{
    return "1.0.0";
}

QString TestPlugin::getPluginDescription() const
{
    return "A minimal test plugin for CFHost framework validation";
}

void TestPlugin::cleanup()
{
    qDebug() << "TestPlugin::cleanup called";
    
    if (m_mainWidget) {
        delete m_mainWidget;
        m_mainWidget = nullptr;
        m_testButton = nullptr;
        m_statusLabel = nullptr;
        m_logTextEdit = nullptr;
    }
    
    m_serviceProvider = nullptr;
    m_initialized = false;
    
    qDebug() << "TestPlugin cleanup completed";
}

void TestPlugin::onButtonClicked()
{
    qDebug() << "TestPlugin::onButtonClicked called";
    
    if (!m_logTextEdit) return;
    
    static int clickCount = 0;
    clickCount++;
    
    QString message = QString("Button clicked %1 times").arg(clickCount);
    m_logTextEdit->append(message);
    
    // 测试服务提供者功能
    if (m_serviceProvider) {
        QString providerVersion = m_serviceProvider->getProviderVersion();
        m_logTextEdit->append(QString("Service provider version: %1").arg(providerVersion));
        
        // 尝试获取一个服务（即使不存在也没关系，这只是测试）
        auto service = m_serviceProvider->getService("test");
        if (service) {
            m_logTextEdit->append("Successfully got 'test' service");
        } else {
            m_logTextEdit->append("'test' service not available (expected)");
        }
    }
    
    qDebug() << message;
}

#include "TestPlugin.moc"

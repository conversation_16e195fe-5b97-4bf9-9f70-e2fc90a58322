# CFHost 核心接口文档

本目录包含了CFHost宿主-插件框架的核心接口定义。这些接口定义了宿主与插件之间的契约。

## 接口概述

### IService.h
定义了所有服务类的基接口。服务是宿主向插件提供的功能模块。

**主要方法：**
- `getServiceName()` - 获取服务名称
- `getServiceVersion()` - 获取服务版本
- `initialize()` - 初始化服务
- `cleanup()` - 清理服务资源
- `isInitialized()` - 检查服务是否已初始化

### IServiceProvider.h
定义了服务提供者接口。宿主通过实现此接口向插件提供服务。

**主要方法：**
- `getService(serviceName)` - 获取指定名称的服务
- `hasService(serviceName)` - 检查是否存在指定服务
- `getAvailableServices()` - 获取所有可用服务列表
- `getProviderVersion()` - 获取服务提供者版本

### IPlugin.h
定义了插件必须实现的接口。这是插件与宿主交互的核心契约。

**主要方法：**
- `init(serviceProvider)` - 初始化插件，接收服务提供者
- `getWidget()` - 获取插件的UI控件
- `canClose()` - 检查插件是否可以关闭
- `getPluginName()` - 获取插件名称
- `getPluginVersion()` - 获取插件版本
- `getPluginDescription()` - 获取插件描述
- `cleanup()` - 清理插件资源

## 使用示例

### 实现一个服务
```cpp
class MyService : public IService
{
public:
    QString getServiceName() const override { return "MyService"; }
    QString getServiceVersion() const override { return "1.0"; }
    bool initialize() override { /* 初始化逻辑 */ return true; }
    void cleanup() override { /* 清理逻辑 */ }
    bool isInitialized() const override { return m_initialized; }
private:
    bool m_initialized = false;
};
```

### 实现一个插件
```cpp
class MyPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)
    
public:
    bool init(IServiceProvider* serviceProvider) override
    {
        // 获取需要的服务
        auto service = serviceProvider->getService("MyService");
        return service != nullptr;
    }
    
    QWidget* getWidget() override
    {
        return new QLabel("Hello from plugin!");
    }
    
    bool canClose() override { return true; }
    // ... 其他方法实现
};
```

## 注意事项

1. 所有接口都是纯虚基类，必须完全实现所有方法
2. 服务之间相互独立，不存在依赖关系
3. 插件通过服务提供者获取宿主提供的服务
4. 插件的UI控件将被宿主显示在主窗口中
5. 插件可以通过`canClose()`方法阻止窗口关闭

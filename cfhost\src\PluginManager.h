#pragma once

#include "stdafx.h"
#include "PluginConfig.h"

class IPlugin;
class QPluginLoader;

/**
 * @brief 插件管理器
 * 
 * 负责插件的查找、配置读取、加载和管理
 */
class PluginManager : public QObject
{
    Q_OBJECT

public:
    PluginManager(QObject *parent = nullptr);
    virtual ~PluginManager();

    /**
     * @brief 根据appId加载插件
     * @param appId 应用ID
     * @return 加载成功返回true，失败返回false
     */
    bool loadPlugin(const QString& appId);

    /**
     * @brief 获取已加载的插件实例
     * @return 插件实例指针，如果没有加载插件则返回nullptr
     */
    IPlugin* getPluginInstance() const { return m_pluginInstance; }

    /**
     * @brief 获取插件配置
     * @return 插件配置结构体的常量引用
     */
    const PluginConfig& getPluginConfig() const { return m_pluginConfig; }

    /**
     * @brief 检查是否已加载插件
     * @return 已加载返回true，否则返回false
     */
    bool isPluginLoaded() const { return m_pluginInstance != nullptr; }

    /**
     * @brief 卸载当前插件
     */
    void unloadPlugin();

private:
    /**
     * @brief 构建插件配置文件路径
     * @param appId 应用ID
     * @return 配置文件的完整路径
     */
    QString buildConfigPath(const QString& appId) const;

    /**
     * @brief 读取并解析插件配置文件
     * @param configPath 配置文件路径
     * @return 解析成功返回true，失败返回false
     */
    bool parseConfigFile(const QString& configPath);

    /**
     * @brief 构建插件DLL文件路径
     * @param appId 应用ID
     * @param dllFileName DLL文件名
     * @return DLL文件的完整路径
     */
    QString buildDllPath(const QString& appId, const QString& dllFileName) const;

    /**
     * @brief 加载插件DLL并创建实例
     * @param dllPath DLL文件路径
     * @return 加载成功返回true，失败返回false
     */
    bool loadPluginDll(const QString& dllPath);

    /**
     * @brief 记录错误日志并返回false
     * @param message 错误消息
     * @return 始终返回false
     */
    bool logErrorAndFail(const QString& message);

private:
    PluginConfig m_pluginConfig;        // 插件配置
    QPluginLoader* m_pluginLoader;      // Qt插件加载器
    IPlugin* m_pluginInstance;          // 插件实例
    QString m_currentAppId;             // 当前加载的应用ID

    // 禁用拷贝构造和赋值操作
    PluginManager(const PluginManager&) = delete;
    PluginManager& operator=(const PluginManager&) = delete;
};

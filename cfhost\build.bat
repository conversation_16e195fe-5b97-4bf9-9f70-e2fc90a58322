@echo off
echo Building CFHost project...

REM 检查是否设置了QTDIR环境变量
if "%QTDIR%"=="" (
    echo Error: QTDIR environment variable is not set.
    echo Please set QTDIR to your Qt installation directory.
    echo Example: set QTDIR=C:\Qt\6.5.0\msvc2022_64
    pause
    exit /b 1
)

echo Using Qt from: %QTDIR%

REM 使用MSBuild编译项目
msbuild cfhost.sln /p:Configuration=Debug /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Executable location: bin\Debug\cfhost.exe
) else (
    echo Build failed!
)

pause
